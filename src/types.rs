use std::collections::HashMap;

/// Temporary JSON value holder for recursive processing
#[derive(Debug, <PERSON>lone)]
pub enum TempJsonValue {
    Null,
    <PERSON>olean(bool),
    Number(f64),
    String(String),
    Array(Vec<TempJsonValue>),
    Object(HashMap<String, TempJsonValue>),
}

/// Recursive JSON type representation
#[derive(Debug, Clone)]
pub enum InferredJsonType {
    Null,
    Boolean,
    Number,
    String,
    Array {
        element_type: Box<InferredJsonType>,
    },
    Object {
        fields: Vec<(String, InferredJsonType)>,
    },
}

/// Schema inference config
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub enable_debug_output: bool,
}
